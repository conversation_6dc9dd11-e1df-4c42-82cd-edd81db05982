@import url('open-iconic/font/css/open-iconic-bootstrap.min.css');
.fondoDesarrollo {
    background-color: #1d301d !important;
}
html, body {
    font-family: system-ui;
}

html, body, #app {
    height: 100%;
    overflow: hidden;
}

#app {
    background-color: inherit;
}
h1:focus {
    outline: none;
}

a, .btn-link {
    color: #0071c1;
}
.popupEditor {
    --dxbl-popup-max-width: 1280px !important;
    max-width: 1280px !important;
    width: 1280px !important;
    min-width: 1280px !important;
} 
/*.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}*/

/*.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}*/

.content {
    padding-top: 1.1rem;
}

.valid.modified:not(.dxbl-checkbox) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

.iconInButton {
    padding: 0 0.75rem 0 0 !important;
    top: -2px !important;
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: #e0e0e0;
        stroke-width: 0.6rem;
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: #1b6ec2;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Cargando");
    }



.ch-800 {
    height: 800px !important;
}

.dxbl-popup-cell {
    z-index: 998 !important;
}

.blazored-toast {
    width: 30rem !important;
}

.blazored-toast-message {
    font-size: 1.2rem !important;
}
.ToTop > .dxbl-fl-ctrl.dxbl-fl-ctrl-nc {
    margin-top: 0px !important;
}
.toolbarItem1 {
    border-right: 3px white solid !important;
    min-width: 60px !important;
}

.toolbarItem2 {
    min-width: 60px !important;
}

.ui-state-highlight {
    background-color: rgba(var(--bs-primary-rgb), .3);
}

.ui-state-hover {
    background: rgba(var(--bs-primary-rgb), .3);
}

.ui-draggable-dragging {
    box-shadow: 0 2px 6px 0 rgb(0 0 0 / 12%);
}

.dxbl-grid .dxbl-grid-table > tbody > tr[data-visible-index] {
    cursor: pointer;
}
.ch-700 {
    height: 700px !important;
}
.ch-390 {
    height: 390px !important;
    max-height: 390px !important;
}
.ch-150 {
    height: 150px !important;
    max-height: 150px !important;
}
.btnSizeL {
    width: 90px !important;
    height: 45px !important;
}
.btnSizeXL {
    width: 90px !important;
    height: 90px !important;
}
.btnSizeM {
    width: 60px !important;
    height: 25px !important;
}
    .btnSizeM.izq {
        border-right: 3px white solid !important;
    }
    .btnSizeM.der {
        margin-left: -5px !important;
    }

.transparente {
    background-color: transparent !important;
}
 .ch-800 {
    height: 700px !important;
}

.highlighted-item {
    background-color: lightyellow !important;
    color: black;
}

.negrita {
    font-weight: bold !important;
}
.checkVerde {
    --dxbl-checkbox-check-element-checked-bg: #adff2f !important;

}
    .checkVerde .dxbl-checkbox {
        color: greenyellow !important;
        --dxbl-checkbox-check-element-checked-bg: #adff2f !important;
    }


.movMaq.dxbl-btn-first {
    padding: 3px 20px;
}

.movMaq.dxbl-btn-last {
    padding: 3px 20px;
    border-left: 1px whitesmoke solid !important;
}