@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO

<div class="litografia-v2-container">
    <!-- Cabecera del grupo -->
    <div class="card mb-3">
        <div class="card-header bg-primary text-white">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4 class="mb-0">@Cabecera</h4>
                </div>
                <div class="col-md-4 text-end">
                    @if (ListaPedidos.Any())
                    {
                        <small>Posiciones: @($"{ListaPedidos.Min(item => item.Posicion):F0} - {ListaPedidos.Max(item => item.Posicion):F0}")</small>
                    }
                </div>
            </div>
        </div>

        <!-- Tabla de datos usando DxDataGrid -->
        <div class="card-body p-0">
            <DxDataGrid Data="@ListaPedidos"
                        CssClass="litografia-grid"
                        ShowPager="false"
                        ShowFilterRow="false"
                        ShowGroupPanel="false"
                        AllowSelectRowByClick="false">
                
                <!-- Cliente -->
                <DxDataGridColumn Field="@nameof(PedidoProgramacionEnPantallaDTO.IdCliente)" 
                                  Caption="Cliente" 
                                  Width="120px">
                    <DisplayTemplate>
                        <div class="cliente-info">
                            <strong>@context.IdCliente</strong><br/>
                            <small>@context.NombreCliente</small>
                        </div>
                    </DisplayTemplate>
                </DxDataGridColumn>

                <!-- Pedido -->
                <DxDataGridColumn Field="@nameof(PedidoProgramacionEnPantallaDTO.Idpedido)" 
                                  Caption="Pedido" 
                                  Width="100px">
                    <DisplayTemplate>
                        <div class="pedido-info">
                            <strong class="fs-5">@($"{context.Idpedido:00-0-0000}")</strong>
                        </div>
                    </DisplayTemplate>
                </DxDataGridColumn>

                <!-- Hojalata -->
                <DxDataGridColumn Field="@nameof(PedidoProgramacionEnPantallaDTO.CaractHjlta)" 
                                  Caption="Hojalata" 
                                  Width="200px">
                    <DisplayTemplate>
                        <div class="hojalata-info">
                            <div>@context.CaractHjlta</div>
                            @if (!string.IsNullOrEmpty(context.Plano))
                            {
                                <small class="text-primary"><strong>Plano:</strong> @context.Plano</small>
                            }
                        </div>
                    </DisplayTemplate>
                </DxDataGridColumn>

                <!-- Hojas -->
                <DxDataGridColumn Field="@nameof(PedidoProgramacionEnPantallaDTO.HojasPedido)" 
                                  Caption="Hojas" 
                                  Width="80px">
                    <DisplayTemplate>
                        <div class="text-center">
                            <strong>@($"{context.HojasPedido:N0}")</strong>
                        </div>
                    </DisplayTemplate>
                </DxDataGridColumn>

                <!-- Diámetro/Formato -->
                <DxDataGridColumn Field="@nameof(PedidoProgramacionEnPantallaDTO.Formato)" 
                                  Caption="Diámetro" 
                                  Width="80px">
                    <DisplayTemplate>
                        <div class="text-center">
                            @if (context.Formato != null)
                            {
                                @($"{context.Formato.Value:F2}")
                            }
                        </div>
                    </DisplayTemplate>
                </DxDataGridColumn>

                <!-- Tintas -->
                <DxDataGridColumn Caption="Tintas" Width="300px">
                    <DisplayTemplate>
                        <div class="tintas-container">
                            <div class="row">
                                <div class="col-6">
                                    @foreach (var tinta in GetListaTintas_T01_T06(context))
                                    {
                                        <div class="tinta-row">
                                            <span class="tinta-nombre">@tinta.T</span>
                                            <span class="tinta-formula">@tinta.FT</span>
                                        </div>
                                    }
                                </div>
                                <div class="col-6">
                                    @{
                                        var mostrarT07 = GetSiMostrarTintaExt(context.Idaplicacion, context.T07Ext);
                                        var mostrarT08 = GetSiMostrarTintaExt(context.Idaplicacion, context.T08Ext);
                                        var mostrarT09 = GetSiMostrarTintaExt(context.Idaplicacion, context.T09Ext);
                                        var mostrarT10 = GetSiMostrarTintaExt(context.Idaplicacion, context.T10Ext);
                                        var mostrarT11 = GetSiMostrarTintaExt(context.Idaplicacion, context.T11Ext);
                                    }
                                    @if (mostrarT07)
                                    {
                                        <div class="tinta-row">
                                            <span class="tinta-nombre">@context.T07Ext</span>
                                            <span class="tinta-formula">@context.FT07Ext</span>
                                        </div>
                                    }
                                    @if (mostrarT08)
                                    {
                                        <div class="tinta-row">
                                            <span class="tinta-nombre">@context.T08Ext</span>
                                            <span class="tinta-formula">@context.FT08Ext</span>
                                        </div>
                                    }
                                    @if (mostrarT09)
                                    {
                                        <div class="tinta-row">
                                            <span class="tinta-nombre">@context.T09Ext</span>
                                            <span class="tinta-formula">@context.FT09Ext</span>
                                        </div>
                                    }
                                    @if (mostrarT10)
                                    {
                                        <div class="tinta-row">
                                            <span class="tinta-nombre">@context.T10Ext</span>
                                            <span class="tinta-formula">@context.FT10Ext</span>
                                        </div>
                                    }
                                    @if (mostrarT11)
                                    {
                                        <div class="tinta-row">
                                            <span class="tinta-nombre">@context.T11Ext</span>
                                            <span class="tinta-formula">@context.FT11Ext</span>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </DisplayTemplate>
                </DxDataGridColumn>

                <!-- Acciones y Estado -->
                <DxDataGridColumn Caption="Acciones" Width="120px">
                    <DisplayTemplate>
                        <div class="acciones-container">
                            <!-- Botones de estado -->
                            <div class="btn-group btn-group-sm mb-2" role="group">
                                <button type="button"
                                        class="@GetButtonClass(context, Enums.ProgramacionesPantalla_EstadosPedido.Empezado)"
                                        title="Empezar orden"
                                        disabled="@(!EsMaquina)"
                                        @onclick="@(() => CambiarEstado(context, Enums.ProgramacionesPantalla_EstadosPedido.Empezado))">
                                    <i class="oi oi-media-play"></i>
                                </button>
                                <button type="button"
                                        class="@GetButtonClass(context, Enums.ProgramacionesPantalla_EstadosPedido.Detenido)"
                                        title="Detener orden"
                                        disabled="@(!EsMaquina)"
                                        @onclick="@(() => CambiarEstado(context, Enums.ProgramacionesPantalla_EstadosPedido.Detenido))">
                                    <i class="oi oi-media-stop"></i>
                                </button>
                                <button type="button"
                                        class="@GetButtonClass(context, Enums.ProgramacionesPantalla_EstadosPedido.Terminado)"
                                        title="Finalizar orden"
                                        disabled="@(!EsMaquina)"
                                        @onclick="@(() => CambiarEstado(context, Enums.ProgramacionesPantalla_EstadosPedido.Terminado))">
                                    <i class="oi oi-circle-check"></i>
                                </button>
                                <button type="button"
                                        class="@GetButtonClass(context, Enums.ProgramacionesPantalla_EstadosPedido.Retirado)"
                                        title="Sacar orden"
                                        disabled="@(!EsMaquina)"
                                        @onclick="@(() => CambiarEstado(context, Enums.ProgramacionesPantalla_EstadosPedido.Retirado))">
                                    <i class="oi oi-circle-x"></i>
                                </button>
                            </div>
                            
                            <!-- Botones de acción -->
                            <div class="btn-group btn-group-sm">
                                <DxButton Click="@(async () => await SetDatosPopUp(context, "Rodillo"))"
                                          Visible="@EsRodillos" 
                                          RenderStyle="ButtonRenderStyle.Info" 
                                          SizeMode="SizeMode.Small"
                                          Text="Rodillo"/>
                                <DxButton Click="@(async () => await SetDatosPopUp(context, "Nota"))"
                                          Visible="@(EsEncargado || EsJefeTurno)" 
                                          RenderStyle="ButtonRenderStyle.Info" 
                                          SizeMode="SizeMode.Small"
                                          Text="Nota"/>
                            </div>
                        </div>
                    </DisplayTemplate>
                </DxDataGridColumn>
            </DxDataGrid>
        </div>

        <!-- Información adicional expandible -->
        @foreach (var item in ListaPedidos)
        {
            <div class="card-body border-top">
                <div class="row">
                    <!-- Información del rodillo -->
                    <div class="col-md-4">
                        <div class="info-section">
                            <h6 class="text-primary">Rodillo</h6>
                            <div class="border p-2 bg-light" style="white-space: pre-wrap;">@item.Rodillo</div>
                        </div>
                    </div>

                    <!-- Información adicional -->
                    <div class="col-md-4">
                        <div class="info-section">
                            <h6 class="text-primary">Información Adicional</h6>
                            <div><small class="text-primary"><strong>Min. Hojas:</strong></small> @item.MinHojas</div>
                            <div><small class="text-primary"><strong>Formato:</strong></small>
                                @if (item.Formato != null)
                                {
                                    @($"{item.Formato.Value:F2}")
                                }
                            </div>
                            <div><strong>Flejar:</strong> @(item.Flejar != null && item.Flejar.Value ? "SI" : "NO")</div>
                        </div>
                    </div>

                    <!-- Fechas y posición -->
                    <div class="col-md-4">
                        <div class="info-section">
                            <h6 class="text-primary">Programación</h6>
                            <div><strong>Posición:</strong> @item.Posicion</div>
                            <div><strong>Inicio:</strong> @(item.HoraComienzoEstimada?.ToString("dd/MM - HH:mm"))</div>
                            <div><strong>Fin:</strong> @(item.HoraFinEstimada?.ToString("dd/MM - HH:mm"))</div>
                            @if (item.RequeridoEnFecha.HasValue)
                            {
                                <div><strong>Requerido:</strong> @(item.RequeridoEnFecha.Value.ToString("dd/MM"))</div>
                            }
                            @if (item.Urgente.HasValue && item.Urgente.Value)
                            {
                                <div class="text-danger"><strong>URGENTE</strong></div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Motivo -->
                @if (!string.IsNullOrEmpty(item.Motivo))
                {
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Motivo</h6>
                            <div class="fs-5">@item.Motivo</div>
                        </div>
                    </div>
                }

                <!-- Observaciones -->
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-primary">Observaciones</h6>
                        <div>@($"{item.Observaciones} {item.Obspaseposterior} {item.ObsCalidad}")</div>
                        @if (!string.IsNullOrEmpty(item.NotaJefeTurno))
                        {
                            <div class="alert alert-danger mt-2" style="white-space: pre-wrap;">
                                <strong>NOTA:</strong> @item.NotaJefeTurno
                            </div>
                        }
                    </div>
                </div>

                <!-- Aplicación Simultánea -->
                @if (item.ApliSimul is not null)
                {
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6 class="text-primary">Aplicación Simultánea</h6>
                            <div><strong>Aplicación Simultánea:</strong> @item.ApliProducto</div>
                            <div><strong>Forma Flejado:</strong> @item.FormaFlejado</div>
                        </div>
                    </div>
                }

                <!-- Información de estado y ubicación -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div>@item.CogerDe</div>
                        <div><strong>Máculas:</strong> @(item.Maculas != null && item.Maculas.Value ? "SI" : "NO")</div>
                    </div>
                    <div class="col-md-6">
                        <div>@item.TextoEstadoCodApli</div>
                    </div>
                </div>
            </div>
        }

        <!-- Sumario del grupo -->
        @if (ListaPedidos.Any())
        {
            <div class="card-footer bg-secondary text-white">
                <div class="row text-center">
                    <div class="col">
                        <strong>Hojas:</strong> @($"{ListaPedidos.Sum(item => item.HojasPedido):N0}")
                    </div>
                    <div class="col">
                        <strong>Superficie:</strong> @($"{ListaPedidos.Sum(item => item.Sup):N2}") m²
                    </div>
                    <div class="col">
                        <strong>Fin:</strong> @(ListaPedidos.Where(o => o.HoraFinEstimada != null)
                                                          .Max(item => item.HoraFinEstimada.Value)
                                                          .ToString("dd/MM/yyyy HH:mm"))
                    </div>
                    <div class="col">
                        <strong>Barniz:</strong> @($"{ListaPedidos.Where(o => o.BarnizNecesario.HasValue).Sum(item => item.BarnizNecesario):N2}") kg
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public List<PedidoProgramacionEnPantallaDTO> ListaPedidos { get; set; }
    [Parameter] public string Cabecera { get; set; }

    // Callbacks para comunicación con el componente padre
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, string, Task> OnObservacionUpdated { get; set; }
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, Enums.ProgramacionesPantalla_EstadosPedido, Task> OnEstadoPedidoUpdatedViaHub { get; set; }
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, Task> OnShowPopup { get; set; }

    // Propiedades de roles pasadas desde el padre
    [Parameter] public bool EsJefeTurno { get; set; }
    [Parameter] public bool EsEncargado { get; set; }
    [Parameter] public bool EsRodillos { get; set; }
    [Parameter] public bool EsMaquina { get; set; }

    // Método para cambiar el estado de un pedido
    private async Task CambiarEstado(PedidoProgramacionEnPantallaDTO pedido, Enums.ProgramacionesPantalla_EstadosPedido nuevoEstado)
    {
        await OnEstadoPedidoUpdatedViaHub(pedido, nuevoEstado);
    }

    // Método para obtener las clases CSS del botón según el estado
    private string GetButtonClass(PedidoProgramacionEnPantallaDTO item, Enums.ProgramacionesPantalla_EstadosPedido estado)
    {
        var baseClass = "btn btn-sm";
        var isSelected = item.IdEstado == (int)estado;

        if (isSelected)
        {
            return $"{baseClass} btn-info";
        }
        else
        {
            return $"{baseClass} btn-light";
        }
    }

    // Método para obtener la clase CSS de la fila según el estado
    private string GetRowClass(GridDataRowInfo rowInfo)
    {
        if (rowInfo.DataItem is PedidoProgramacionEnPantallaDTO item)
        {
            var estadoNombre = item.NombreEstado?.ToLower().Trim() ?? "sinempezar";
            return $"row-estado-{estadoNombre}";
        }
        return "";
    }

    private async Task SetDatosPopUp(PedidoProgramacionEnPantallaDTO pedido, string texto)
    {
        await OnShowPopup(pedido, texto);
    }

    private List<(string T, string FT)> GetListaTintas_T01_T06(PedidoProgramacionEnPantallaDTO pedido)
    {
        List<(string T, string FT)> lista = new();

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T01Ext))
            lista.Add((pedido.T01Ext, pedido.FT01Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T01Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T02Ext))
            lista.Add((pedido.T02Ext, pedido.FT02Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T02Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T03Ext))
            lista.Add((pedido.T03Ext, pedido.FT03Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T03Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T04Ext))
            lista.Add((pedido.T04Ext, pedido.FT04Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T04Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T05Ext))
            lista.Add((pedido.T05Ext, pedido.FT05Ext));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T06Ext))
            lista.Add((pedido.T06Ext, pedido.FT06Ext));
        else
            lista.Add(("", ""));

        return lista;
    }

    private bool GetSiMostrarTintaExt(int? idAplicacion, string? T)
    {
        // las tintas exteriores únicamente pueden ir con esta config
        return idAplicacion == 999 || (idAplicacion == 997 && T.Contains("1000"));
    }

    private bool GetSiMostrarTintaInt(int? idAplicacion)
    {
        // las tintas interiores únicamente pueden ir con esta config
        return idAplicacion == 999 || idAplicacion == 998; // TODO: preguntar si es necesario la 999
    }
}
