/* Estilos específicos para los componentes VerProgramacionV2 */
/* Replicando los estilos visuales de los reportes DevExpress */

/* Contenedores principales */
.litografia-v2-container,
.barnizado-v2-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 12px;
}

/* Cabeceras de grupo - Replicando GroupHeader del reporte */
.cabecera-grupo {
    background-color: #0d6efd;
    color: white;
    font-weight: bold;
    padding: 8px;
    border-radius: 4px 4px 0 0;
}

.subcabeceraGrupo {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    padding: 4px 0;
}

.subcabeceraGrupo h6 {
    margin: 0;
    font-size: 11px;
    text-align: center;
}

/* Estados de filas - Replicando los colores condicionales del reporte */
.row-estado-sinempezar {
    background-color: #ffffff;
}

.row-estado-empezado {
    background-color: #fff3cd; /* Amarillo claro */
}

.row-estado-detenido {
    background-color: #f8d7da; /* Rojo claro */
}

.row-estado-terminado {
    background-color: #d1edff; /* Azul claro */
}

.row-estado-retirado {
    background-color: #d4edda; /* Verde claro */
}

/* Información de cliente */
.cliente-info {
    font-size: 11px;
}

.cliente-info strong {
    font-size: 12px;
    color: #0d6efd;
}

/* Información de pedido */
.pedido-info {
    font-weight: bold;
}

/* Información de hojalata */
.hojalata-info {
    font-size: 11px;
}

/* Contenedor de tintas - Replicando la estructura de tintas del reporte */
.tintas-container {
    font-size: 10px;
}

.tinta-row {
    margin-bottom: 2px;
    line-height: 1.2;
}

.tinta-nombre {
    font-weight: bold;
    color: #0d6efd;
}

.tinta-formula {
    color: #6c757d;
    font-style: italic;
}

/* Contenedor de acciones */
.acciones-container {
    text-align: center;
}

.acciones-container .btn {
    margin: 1px;
    font-size: 10px;
    padding: 2px 4px;
}

/* Información adicional */
.info-section {
    margin-bottom: 8px;
}

.info-section h6 {
    font-size: 11px;
    margin-bottom: 4px;
    color: #0d6efd;
    font-weight: bold;
}

.info-section .small {
    font-size: 10px;
    line-height: 1.3;
}

/* Observaciones */
.observaciones-container {
    font-size: 11px;
}

.observaciones-container .text-danger {
    background-color: #f8d7da;
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid #f5c6cb;
}

/* Sumario - Replicando GroupFooter del reporte */
.sumario {
    background-color: #6c757d;
    color: white;
    padding: 8px;
    display: flex;
    justify-content: space-around;
    font-weight: bold;
    font-size: 11px;
    border-radius: 0 0 4px 4px;
}

.sumario span {
    text-align: center;
    flex: 1;
}

/* Estilos específicos para barnizado */
.barnizado-v2-container .card-header {
    background-color: #198754 !important; /* Verde para barnizado */
}

/* Estilos específicos para litografía */
.litografia-v2-container .card-header {
    background-color: #0d6efd !important; /* Azul para litografía */
}

/* Botones de estado */
.btn-estado {
    border-radius: 3px;
    font-size: 10px;
    padding: 2px 6px;
}

.btn-estado.active {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .litografia-v2-container,
    .barnizado-v2-container {
        font-size: 10px;
    }
    
    .subcabeceraGrupo h6 {
        font-size: 9px;
    }
    
    .tintas-container {
        font-size: 9px;
    }
    
    .acciones-container .btn {
        font-size: 8px;
        padding: 1px 3px;
    }
}

/* Ajustes para DxFormLayout */
.dx-form-layout-group {
    margin-bottom: 0;
}

.dx-form-layout-item {
    padding: 2px;
}

/* Bordes y separadores */
.border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
}

/* Texto de urgente */
.text-urgente {
    color: #dc3545;
    font-weight: bold;
    font-size: 12px;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* Estilos para tubería */
.tuberia-indicator {
    color: #dc3545;
    font-weight: bold;
    text-decoration: underline;
}

/* Estilos para tipo de lavada */
.tipo-lavada {
    color: #dc3545;
    font-weight: bold;
}

/* Ajustes para campos de rodillo */
.rodillo-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px;
    font-family: 'Courier New', monospace;
    font-size: 10px;
    white-space: pre-wrap;
    min-height: 40px;
    max-height: 80px;
    overflow-y: auto;
}

/* Estilos para notas del jefe de turno */
.nota-jefe-turno {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 4px;
    margin-top: 4px;
    font-size: 10px;
    white-space: pre-wrap;
}

/* Estilos para observaciones de almacén */
.obs-almacen {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    padding: 2px 4px;
    margin-top: 2px;
    font-size: 10px;
}

/* Ajustes para campos de fecha y hora */
.fecha-hora {
    font-size: 10px;
    color: #6c757d;
}

/* Estilos para posición */
.posicion {
    font-weight: bold;
    color: #0d6efd;
}

/* Estilos para formato/diámetro */
.formato {
    font-weight: bold;
    text-align: center;
}

/* Estilos para cantidad de hojas */
.hojas {
    font-weight: bold;
    text-align: center;
    color: #198754;
}

/* Estilos para superficie */
.superficie {
    font-weight: bold;
    color: #fd7e14;
}

/* Estilos para barniz necesario */
.barniz-necesario {
    font-weight: bold;
    color: #6f42c1;
}
