@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO

<div class="litografia-v2-container">
    <!-- Replicando la estructura del LitografiaReport usando DxFormLayout -->
    <DxFormLayoutGroup CssClass="mt-0">
        <HeaderTemplate>
            <!-- GroupHeader2 del reporte - Cabecera del grupo -->
            <div class="card-header bg-primary text-white p-2">
                <div class="row align-items-center">
                    <div class="col-md-10">
                        <h4 class="mb-0 fw-bold">@Cabecera</h4>
                    </div>
                    <div class="col-md-2 text-end">
                        @if (ListaPedidos.Any())
                        {
                            <small class="fw-bold">Pos: @($"{ListaPedidos.Min(item => item.Posicion):F0}-{ListaPedidos.Max(item => item.Posicion):F0}")</small>
                        }
                    </div>
                </div>
            </div>
        </HeaderTemplate>
        <Items>
            <!-- Subcabecera con títulos de columnas - Replicando la estructura de tabla del reporte -->
            <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" CssClass="subcabeceraGrupo">
                <DxFormLayoutItem ColSpanLg="2" BeginRow="true">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Cliente</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="1">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Pedido</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="2">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Hojalata</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="1">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Hojas</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="1">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Diámetro</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="4">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Tintas</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="1">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Acciones</h6>
                    </span>
                </DxFormLayoutItem>
            </DxFormLayoutGroup>

            <!-- Detail Band - Iteración de registros usando DxFormLayoutGroup para cada pedido -->
            @foreach (var item in ListaPedidos.OrderBy(x => x.Posicion))
            {
                <DxFormLayoutGroup CssClass="@(GetRowCssClass(item)) border-bottom"
                                   Id="@item.Idpedido.ToString()"
                                   Decoration="FormLayoutGroupDecoration.None">
                    
                    <!-- Fila principal de datos - Replicando los XRLabel del Detail Band -->
                    <DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
                        <!-- Cliente - Replicando xrLabel4: [IdCliente]+', '+[NombreCliente] -->
                        <DxFormLayoutItem ColSpanLg="2">
                            <div class="cliente-info p-2">
                                <strong>@item.IdCliente</strong><br/>
                                <small>@item.NombreCliente</small>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Pedido - Replicando xrLabel7: [Idpedido] -->
                        <DxFormLayoutItem ColSpanLg="1">
                            <div class="pedido-info text-center p-2">
                                <strong class="fs-5">@($"{item.Idpedido:00-0-0000}")</strong>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Hojalata - Replicando xrLabel6: [CaractHjlta] -->
                        <DxFormLayoutItem ColSpanLg="2">
                            <div class="hojalata-info p-2">
                                <div>@item.CaractHjlta</div>
                                @if (!string.IsNullOrEmpty(item.Plano))
                                {
                                    <small class="text-primary"><strong>Plano:</strong> @item.Plano</small>
                                }
                            </div>
                        </DxFormLayoutItem>

                        <!-- Hojas - Replicando xrLabel5: FormatString('{0:#,##0}', [HojasPedido]) -->
                        <DxFormLayoutItem ColSpanLg="1">
                            <div class="text-center p-2">
                                <strong>@($"{item.HojasPedido:N0}")</strong>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Diámetro - Replicando xrLabel8: [Formato] -->
                        <DxFormLayoutItem ColSpanLg="1">
                            <div class="text-center p-2">
                                @if (item.Formato != null)
                                {
                                    @($"{item.Formato.Value:F2}")
                                }
                            </div>
                        </DxFormLayoutItem>

                        <!-- Tintas - Replicando la lógica de visibilidad de tintas del reporte -->
                        <DxFormLayoutItem ColSpanLg="4">
                            <div class="tintas-container p-2">
                                <div class="row g-1">
                                    <div class="col-6">
                                        @foreach (var tinta in GetListaTintas_T01_T06(item))
                                        {
                                            @if (!string.IsNullOrEmpty(tinta.T))
                                            {
                                                <div class="tinta-row d-flex justify-content-between small">
                                                    <span class="tinta-nombre">@tinta.T</span>
                                                    <span class="tinta-formula">@tinta.FT</span>
                                                </div>
                                            }
                                        }
                                    </div>
                                    <div class="col-6">
                                        @{
                                            var mostrarT07 = GetSiMostrarTintaExt(item.Idaplicacion, item.T07Ext);
                                            var mostrarT08 = GetSiMostrarTintaExt(item.Idaplicacion, item.T08Ext);
                                            var mostrarT09 = GetSiMostrarTintaExt(item.Idaplicacion, item.T09Ext);
                                            var mostrarT10 = GetSiMostrarTintaExt(item.Idaplicacion, item.T10Ext);
                                            var mostrarT11 = GetSiMostrarTintaExt(item.Idaplicacion, item.T11Ext);
                                        }
                                        @if (mostrarT07)
                                        {
                                            <div class="tinta-row d-flex justify-content-between small">
                                                <span class="tinta-nombre">@item.T07Ext</span>
                                                <span class="tinta-formula">@item.FT07Ext</span>
                                            </div>
                                        }
                                        @if (mostrarT08)
                                        {
                                            <div class="tinta-row d-flex justify-content-between small">
                                                <span class="tinta-nombre">@item.T08Ext</span>
                                                <span class="tinta-formula">@item.FT08Ext</span>
                                            </div>
                                        }
                                        @if (mostrarT09)
                                        {
                                            <div class="tinta-row d-flex justify-content-between small">
                                                <span class="tinta-nombre">@item.T09Ext</span>
                                                <span class="tinta-formula">@item.FT09Ext</span>
                                            </div>
                                        }
                                        @if (mostrarT10)
                                        {
                                            <div class="tinta-row d-flex justify-content-between small">
                                                <span class="tinta-nombre">@item.T10Ext</span>
                                                <span class="tinta-formula">@item.FT10Ext</span>
                                            </div>
                                        }
                                        @if (mostrarT11)
                                        {
                                            <div class="tinta-row d-flex justify-content-between small">
                                                <span class="tinta-nombre">@item.T11Ext</span>
                                                <span class="tinta-formula">@item.FT11Ext</span>
                                            </div>
                                        }
                                        <div class="mt-1 small">
                                            <strong>Flejar: @(item.Flejar != null && item.Flejar.Value ? "SI" : "NO")</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Acciones y Estado -->
                        <DxFormLayoutItem ColSpanLg="1">
                            <div class="acciones-container p-2">
                                <!-- Botones de estado -->
                                <div class="btn-group-vertical btn-group-sm mb-2 d-grid gap-1" role="group">
                                    <DxButton Click="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado))"
                                              CssClass="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado)"
                                              Title="Empezar orden"
                                              Enabled="@EsMaquina"
                                              SizeMode="SizeMode.Small">
                                        <i class="oi oi-media-play"></i>
                                    </DxButton>
                                    <DxButton Click="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido))"
                                              CssClass="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido)"
                                              Title="Detener orden"
                                              Enabled="@EsMaquina"
                                              SizeMode="SizeMode.Small">
                                        <i class="oi oi-media-stop"></i>
                                    </DxButton>
                                    <DxButton Click="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado))"
                                              CssClass="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado)"
                                              Title="Finalizar orden"
                                              Enabled="@EsMaquina"
                                              SizeMode="SizeMode.Small">
                                        <i class="oi oi-circle-check"></i>
                                    </DxButton>
                                    <DxButton Click="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado))"
                                              CssClass="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado)"
                                              Title="Sacar orden"
                                              Enabled="@EsMaquina"
                                              SizeMode="SizeMode.Small">
                                        <i class="oi oi-circle-x"></i>
                                    </DxButton>
                                </div>
                                
                                <!-- Botones de acción -->
                                <div class="btn-group-vertical btn-group-sm d-grid gap-1">
                                    <DxButton Click="@(async () => await SetDatosPopUp(item, "Rodillo"))"
                                              Visible="@EsRodillos" 
                                              RenderStyle="ButtonRenderStyle.Info" 
                                              SizeMode="SizeMode.Small"
                                              Text="Rod"/>
                                    <DxButton Click="@(async () => await SetDatosPopUp(item, "Nota"))"
                                              Visible="@(EsEncargado || EsJefeTurno)" 
                                              RenderStyle="ButtonRenderStyle.Info" 
                                              SizeMode="SizeMode.Small"
                                              Text="Nota"/>
                                </div>
                            </div>
                        </DxFormLayoutItem>
                    </DxFormLayoutGroup>
                </DxFormLayoutGroup>
            }

            <!-- Sumario del grupo - Replicando GroupFooter del reporte -->
            @if (ListaPedidos.Any())
            {
                <span class="sumario">
                    <span>@($"{ListaPedidos.Min(item => item.Posicion):F0} - {ListaPedidos.Max(item => item.Posicion):F0}")</span>
                    <span>Hojas: @($"{ListaPedidos.Sum(item => item.HojasPedido):N0}")</span>
                    <span>Superficie: @($"{ListaPedidos.Sum(item => item.Sup):N2}") m2</span>
                    <span>
                        Fin: @(ListaPedidos.Where(o => o.HoraFinEstimada != null)
                                          .Max(item => item.HoraFinEstimada.Value)
                                          .ToString("dd/MM/yyyy hh:mm"))
                    </span>
                    <span>Barniz: @($"{ListaPedidos.Where(o => o.BarnizNecesario.HasValue).Sum(item => item.BarnizNecesario):N2}") kg</span>
                </span>
            }
        </Items>
    </DxFormLayoutGroup>
</div>

@code {
    [Parameter] public List<PedidoProgramacionEnPantallaDTO> ListaPedidos { get; set; }
    [Parameter] public string Cabecera { get; set; }

    // Callbacks para comunicación con el componente padre
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, string, Task> OnObservacionUpdated { get; set; }
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, Enums.ProgramacionesPantalla_EstadosPedido, Task> OnEstadoPedidoUpdatedViaHub { get; set; }
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, Task> OnShowPopup { get; set; }

    // Propiedades de roles pasadas desde el padre
    [Parameter] public bool EsJefeTurno { get; set; }
    [Parameter] public bool EsEncargado { get; set; }
    [Parameter] public bool EsRodillos { get; set; }
    [Parameter] public bool EsMaquina { get; set; }

    // Método para cambiar el estado de un pedido
    private async Task CambiarEstado(PedidoProgramacionEnPantallaDTO pedido, Enums.ProgramacionesPantalla_EstadosPedido nuevoEstado)
    {
        await OnEstadoPedidoUpdatedViaHub(pedido, nuevoEstado);
    }

    // Método para obtener las clases CSS del botón según el estado
    private string GetButtonClass(PedidoProgramacionEnPantallaDTO item, Enums.ProgramacionesPantalla_EstadosPedido estado)
    {
        var baseClass = "btn btn-sm";
        var isSelected = item.IdEstado == (int)estado;

        if (isSelected)
        {
            return $"{baseClass} btn-info";
        }
        else
        {
            return $"{baseClass} btn-light";
        }
    }

    // Método para obtener la clase CSS de la fila según el estado
    private string GetRowCssClass(PedidoProgramacionEnPantallaDTO item)
    {
        var estadoNombre = item.NombreEstado?.ToLower().Trim() ?? "sinempezar";
        return $"row-estado-{estadoNombre}";
    }

    private async Task SetDatosPopUp(PedidoProgramacionEnPantallaDTO pedido, string texto)
    {
        await OnShowPopup(pedido, texto);
    }

    private List<(string T, string FT)> GetListaTintas_T01_T06(PedidoProgramacionEnPantallaDTO pedido)
    {
        List<(string T, string FT)> lista = new();

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T01Ext))
            lista.Add((pedido.T01Ext, pedido.FT01Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T01Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T02Ext))
            lista.Add((pedido.T02Ext, pedido.FT02Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T02Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T03Ext))
            lista.Add((pedido.T03Ext, pedido.FT03Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T03Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T04Ext))
            lista.Add((pedido.T04Ext, pedido.FT04Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T04Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T05Ext))
            lista.Add((pedido.T05Ext, pedido.FT05Ext));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T06Ext))
            lista.Add((pedido.T06Ext, pedido.FT06Ext));
        else
            lista.Add(("", ""));

        return lista;
    }

    private bool GetSiMostrarTintaExt(int? idAplicacion, string T)
    {
        // las tintas exteriores únicamente pueden ir con esta config
        return idAplicacion == 999 || (idAplicacion == 997 && !string.IsNullOrEmpty(T) && T.Contains("1000"));
    }

    private bool GetSiMostrarTintaInt(int? idAplicacion)
    {
        // las tintas interiores únicamente pueden ir con esta config
        return idAplicacion == 999 || idAplicacion == 998;
    }
}
