@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO

<div class="barnizado-v2-container">
    <!-- Cabecera del grupo - Replicando GroupHeader1 del reporte (ApliProducto + Orden) -->
    <div class="card mb-3">
        <div class="card-header bg-success text-white p-2">
            <div class="row align-items-center">
                <div class="col-md-7">
                    <h4 class="mb-0 fw-bold">@Cabecera</h4>
                </div>
                <div class="col-md-2 text-center">
                    @if (ListaPedidos.FirstOrDefault() is { Tuberia: true })
                    {
                        <h5 class="mb-0 text-danger fw-bold">(TUBERIA)</h5>
                    }
                </div>
                <div class="col-md-3 text-end">
                    <h5 class="mb-0 text-danger fw-bold">@(ListaPedidos.First().TipoLavada)</h5>
                </div>
            </div>
        </div>

        <!-- Subcabecera con títulos de columnas - Replicando GroupHeader2 del reporte -->
        <div class="card-header bg-light border-bottom p-2">
            <div class="row text-center fw-bold text-primary">
                <div class="col-2">Cliente</div>
                <div class="col-1">Hojas</div>
                <div class="col-2">Hojalata</div>
                <div class="col-1">Pedido</div>
                <div class="col-1">Diámetro</div>
                <div class="col-4">Observaciones</div>
                <div class="col-1">Acciones</div>
            </div>
        </div>

        <!-- Datos usando estructura similar al reporte -->
        <div class="card-body p-0">
            @foreach (var item in ListaPedidos)
            {
                <div class="@GetRowCssClass(item) border-bottom p-2" id="@item.Idpedido.ToString()">
                    <!-- Fila principal de datos - Replicando Detail Band -->
                    <div class="row align-items-start">
                        <!-- Cliente -->
                        <div class="col-2">
                            <div class="cliente-info">
                                <strong>@item.IdCliente</strong><br/>
                                <small>@item.NombreCliente</small>
                            </div>
                        </div>

                        <!-- Hojas -->
                        <div class="col-1 text-center">
                            <strong>@($"{item.HojasPedido:N0}")</strong>
                        </div>

                        <!-- Hojalata -->
                        <div class="col-2">
                            <div class="hojalata-info">
                                <div>@item.CaractHjlta</div>
                            </div>
                        </div>

                        <!-- Pedido -->
                        <div class="col-1 text-center">
                            <div class="pedido-info">
                                <strong class="fs-5">@($"{item.Idpedido:00-0-0000}")</strong>
                            </div>
                        </div>

                        <!-- Diámetro/Formato -->
                        <div class="col-1 text-center">
                            @if (item.Formato != null)
                            {
                                @($"{item.Formato.Value:F2}")
                            }
                        </div>

                        <!-- Observaciones -->
                        <div class="col-4">
                            <div class="observaciones-container">
                                <div class="small">
                                    @($"{item.Observaciones} {item.Obspaseposterior}")
                                    @if (!string.IsNullOrEmpty(item.NotaJefeTurno))
                                    {
                                        <div class="text-danger fw-bold mt-1" style="white-space: pre-wrap;">
                                            <strong>NOTA:</strong> @item.NotaJefeTurno
                                        </div>
                                    }
                                </div>
                                @if (!string.IsNullOrEmpty(item.ObsAlmacen) && item.ObsAlmacen.Trim().Length > 0)
                                {
                                    <div class="mt-1 small text-info">
                                        <strong>Almacén:</strong> @item.ObsAlmacen
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Acciones y Estado -->
                        <div class="col-1">
                            <div class="acciones-container">
                                <!-- Botones de estado -->
                                <div class="btn-group-vertical btn-group-sm mb-2 d-grid gap-1" role="group">
                                    <button type="button"
                                            class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado)"
                                            title="Empezar orden"
                                            disabled="@(!EsMaquina)"
                                            @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado))">
                                        <i class="oi oi-media-play"></i>
                                    </button>
                                    <button type="button"
                                            class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido)"
                                            title="Detener orden"
                                            disabled="@(!EsMaquina)"
                                            @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido))">
                                        <i class="oi oi-media-stop"></i>
                                    </button>
                                    <button type="button"
                                            class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado)"
                                            title="Finalizar orden"
                                            disabled="@(!EsMaquina)"
                                            @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado))">
                                        <i class="oi oi-circle-check"></i>
                                    </button>
                                    <button type="button"
                                            class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado)"
                                            title="Sacar orden"
                                            disabled="@(!EsMaquina)"
                                            @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado))">
                                        <i class="oi oi-circle-x"></i>
                                    </button>
                                </div>
                                
                                <!-- Botones de acción -->
                                <div class="btn-group-vertical btn-group-sm d-grid gap-1">
                                    <DxButton Click="@(async () => await SetDatosPopUp(item, "Rodillo"))"
                                              Visible="@EsRodillos" 
                                              RenderStyle="ButtonRenderStyle.Info" 
                                              SizeMode="SizeMode.Small"
                                              Text="Rod"/>
                                    <DxButton Click="@(async () => await SetDatosPopUp(item, "Nota"))"
                                              Visible="@(EsEncargado || EsJefeTurno)" 
                                              RenderStyle="ButtonRenderStyle.Info" 
                                              SizeMode="SizeMode.Small"
                                              Text="Nota"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Segunda fila con información adicional -->
                    <div class="row mt-2">
                        <!-- Información de urgencia y fechas -->
                        <div class="col-3">
                            <div class="info-section">
                                @if (item.Urgente.HasValue && item.Urgente.Value)
                                {
                                    <div class="text-danger fw-bold">URGENTE</div>
                                }
                                @if (item.RequeridoEnFecha.HasValue)
                                {
                                    <div><strong>Requerido:</strong> @(item.RequeridoEnFecha.Value.ToString("dd/MM"))</div>
                                }
                                @if (!string.IsNullOrEmpty(item.Plano))
                                {
                                    <small class="text-primary"><strong>Plano:</strong> @item.Plano</small>
                                }
                            </div>
                        </div>

                        <!-- Rodillo -->
                        <div class="col-3">
                            <div class="info-section">
                                <h6 class="text-primary mb-1">Rodillo</h6>
                                <div class="border p-2 bg-light small" style="white-space: pre-wrap; min-height: 40px;">@item.Rodillo</div>
                            </div>
                        </div>

                        <!-- Información de calidad y estado -->
                        <div class="col-3">
                            <div class="info-section small">
                                <div>@item.ObsCalidad</div>
                                <div>@item.TextoEstadoCodApli</div>
                            </div>
                        </div>

                        <!-- Información de programación y barniz -->
                        <div class="col-3">
                            <div class="info-section">
                                <div class="small">
                                    <div><strong>Posición:</strong> @item.Posicion</div>
                                    <div><strong>Flejar:</strong> @(item.Flejar.HasValue && item.Flejar.Value ? "SI" : "NO")</div>
                                    <div><strong>Barniz:</strong> @($"{item.BarnizNecesario}")</div>
                                    <div><strong>Inicio:</strong> @(item.HoraComienzoEstimada?.ToString("dd/MM - HH:mm"))</div>
                                    <div><strong>Fin:</strong> @(item.HoraFinEstimada?.ToString("dd/MM - HH:mm"))</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Sumario del grupo - Replicando GroupFooter del reporte -->
        @if (ListaPedidos.Any())
        {
            <div class="card-footer bg-secondary text-white">
                <div class="row text-center">
                    <div class="col">
                        <strong>Pos:</strong> @($"{ListaPedidos.Min(item => item.Posicion):F0} - {ListaPedidos.Max(item => item.Posicion):F0}")
                    </div>
                    <div class="col">
                        <strong>Hojas:</strong> @($"{ListaPedidos.Sum(item => item.HojasPedido):N0}")
                    </div>
                    <div class="col">
                        <strong>Superficie:</strong> @($"{ListaPedidos.Sum(item => item.Sup):N2}") m²
                    </div>
                    <div class="col">
                        @{
                            var listaAux = ListaPedidos.Where(o => o.HoraFinEstimada != null).ToList();
                            var finTexto = listaAux.Any()
                                ? listaAux.Max(item => item.HoraFinEstimada.Value).ToString("dd/MM/yyyy HH:mm")
                                : "";
                        }
                        <strong>Fin:</strong> @finTexto
                    </div>
                    <div class="col">
                        <strong>Barniz:</strong> @($"{ListaPedidos.Where(o => o.BarnizNecesario.HasValue).Sum(item => item.BarnizNecesario):N2}") kg
                    </div>
                </div>
            </div>
        }
    </div>
</div>
