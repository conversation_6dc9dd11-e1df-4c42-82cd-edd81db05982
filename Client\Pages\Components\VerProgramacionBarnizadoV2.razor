@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO

<div class="barnizado-v2-container">
    <!-- Replicando la estructura del BarnizadoReportV2 usando DxFormLayout -->
    <DxFormLayoutGroup CssClass="mt-0">
        <HeaderTemplate>
            <!-- GroupHeader1 del reporte - Cabecera del grupo (ApliProducto + Orden) -->
            <div class="card-header bg-success text-white p-2">
                <div class="row align-items-center">
                    <div class="col-md-7">
                        <h4 class="mb-0 fw-bold">@Cabecera</h4>
                    </div>
                    <div class="col-md-2 text-center">
                        @if (ListaPedidos.FirstOrDefault() is { Tuberia: true })
                        {
                            <h5 class="mb-0 text-danger fw-bold">(TUBERIA)</h5>
                        }
                    </div>
                    <div class="col-md-3 text-end">
                        <h5 class="mb-0 text-danger fw-bold">@(ListaPedidos.First().TipoLavada)</h5>
                    </div>
                </div>
            </div>
        </HeaderTemplate>
        <Items>
            <!-- Subcabecera con títulos de columnas - Replicando GroupHeader2 del reporte -->
            <DxFormLayoutGroup Decoration="FormLayoutGroupDecoration.None" CssClass="subcabeceraGrupo">
                <DxFormLayoutItem ColSpanLg="2" BeginRow="true">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Cliente</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="1">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Hojas</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="2">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Hojalata</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="1">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Pedido</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="1">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Diámetro</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="4">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Observaciones</h6>
                    </span>
                </DxFormLayoutItem>
                <DxFormLayoutItem ColSpanLg="1">
                    <span class="cabecera-grupo">
                        <h6 class="text-primary fw-bold">Acciones</h6>
                    </span>
                </DxFormLayoutItem>
            </DxFormLayoutGroup>

            <!-- Detail Band - Iteración de registros usando DxFormLayoutGroup para cada pedido -->
            @foreach (var item in ListaPedidos.OrderBy(x => x.Posicion))
            {
                <DxFormLayoutGroup CssClass="@(GetRowCssClass(item)) border-bottom"
                                   Id="@item.Idpedido.ToString()"
                                   Decoration="FormLayoutGroupDecoration.None">

                    <!-- Fila principal de datos - Replicando los XRLabel del Detail Band del BarnizadoReportV2 -->
                    <DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
                        <!-- Cliente - Replicando xrLabel4: [IdCliente]+', '+[NombreCliente] -->
                        <DxFormLayoutItem ColSpanLg="2">
                            <div class="cliente-info p-2">
                                <strong>@item.IdCliente</strong><br/>
                                <small>@item.NombreCliente</small>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Hojas - Replicando xrLabel5: FormatString('{0:#,##0}', [HojasPedido]) -->
                        <DxFormLayoutItem ColSpanLg="1">
                            <div class="text-center p-2">
                                <strong>@($"{item.HojasPedido:N0}")</strong>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Hojalata - Replicando xrLabel6: [CaractHjlta] -->
                        <DxFormLayoutItem ColSpanLg="2">
                            <div class="hojalata-info p-2">
                                <div>@item.CaractHjlta</div>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Pedido - Replicando xrLabel7: [Idpedido] -->
                        <DxFormLayoutItem ColSpanLg="1">
                            <div class="pedido-info text-center p-2">
                                <strong class="fs-5">@($"{item.Idpedido:00-0-0000}")</strong>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Diámetro - Replicando xrLabel8: [Formato] -->
                        <DxFormLayoutItem ColSpanLg="1">
                            <div class="text-center p-2">
                                @if (item.Formato != null)
                                {
                                    @($"{item.Formato.Value:F2}")
                                }
                            </div>
                        </DxFormLayoutItem>

                        <!-- Observaciones - Replicando xrRichText1: [Observaciones] + [Obspaseposterior] -->
                        <DxFormLayoutItem ColSpanLg="4">
                            <div class="observaciones-container p-2">
                                <div class="small">
                                    @($"{item.Observaciones} {item.Obspaseposterior}")
                                    @if (!string.IsNullOrEmpty(item.NotaJefeTurno))
                                    {
                                        <div class="text-danger fw-bold mt-1" style="white-space: pre-wrap;">
                                            <strong>NOTA:</strong> @item.NotaJefeTurno
                                        </div>
                                    }
                                </div>
                                @if (!string.IsNullOrEmpty(item.ObsAlmacen) && item.ObsAlmacen.Trim().Length > 0)
                                {
                                    <div class="mt-1 small text-info">
                                        <strong>Almacén:</strong> @item.ObsAlmacen
                                    </div>
                                }
                            </div>
                        </DxFormLayoutItem>

                        <!-- Acciones y Estado -->
                        <DxFormLayoutItem ColSpanLg="1">
                            <div class="acciones-container p-2">
                                <!-- Botones de estado -->
                                <div class="btn-group-vertical btn-group-sm mb-2 d-grid gap-1" role="group">
                                    <DxButton Click="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado))"
                                              CssClass="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado)"
                                              Title="Empezar orden"
                                              Enabled="@EsMaquina"
                                              SizeMode="SizeMode.Small">
                                        <i class="oi oi-media-play"></i>
                                    </DxButton>
                                    <DxButton Click="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido))"
                                              CssClass="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido)"
                                              Title="Detener orden"
                                              Enabled="@EsMaquina"
                                              SizeMode="SizeMode.Small">
                                        <i class="oi oi-media-stop"></i>
                                    </DxButton>
                                    <DxButton Click="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado))"
                                              CssClass="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado)"
                                              Title="Finalizar orden"
                                              Enabled="@EsMaquina"
                                              SizeMode="SizeMode.Small">
                                        <i class="oi oi-circle-check"></i>
                                    </DxButton>
                                    <DxButton Click="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado))"
                                              CssClass="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado)"
                                              Title="Sacar orden"
                                              Enabled="@EsMaquina"
                                              SizeMode="SizeMode.Small">
                                        <i class="oi oi-circle-x"></i>
                                    </DxButton>
                                </div>

                                <!-- Botones de acción -->
                                <div class="btn-group-vertical btn-group-sm d-grid gap-1">
                                    <DxButton Click="@(async () => await SetDatosPopUp(item, "Rodillo"))"
                                              Visible="@EsRodillos"
                                              RenderStyle="ButtonRenderStyle.Info"
                                              SizeMode="SizeMode.Small"
                                              Text="Rod"/>
                                    <DxButton Click="@(async () => await SetDatosPopUp(item, "Nota"))"
                                              Visible="@(EsEncargado || EsJefeTurno)"
                                              RenderStyle="ButtonRenderStyle.Info"
                                              SizeMode="SizeMode.Small"
                                              Text="Nota"/>
                                </div>
                            </div>
                        </DxFormLayoutItem>
                    </DxFormLayoutGroup>

                    <!-- Segunda fila con información adicional - Replicando otros campos del Detail Band -->
                    <DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
                        <!-- Información de urgencia y fechas -->
                        <DxFormLayoutItem ColSpanLg="3">
                            <div class="info-section p-2">
                                @if (item.Urgente.HasValue && item.Urgente.Value)
                                {
                                    <div class="text-danger fw-bold">URGENTE</div>
                                }
                                @if (item.RequeridoEnFecha.HasValue)
                                {
                                    <div><strong>Requerido:</strong> @(item.RequeridoEnFecha.Value.ToString("dd/MM"))</div>
                                }
                                @if (!string.IsNullOrEmpty(item.Plano))
                                {
                                    <small class="text-primary"><strong>Plano:</strong> @item.Plano</small>
                                }
                            </div>
                        </DxFormLayoutItem>

                        <!-- Rodillo - Replicando el área de rodillo del reporte -->
                        <DxFormLayoutItem ColSpanLg="3">
                            <div class="info-section p-2">
                                <h6 class="text-primary mb-1">Rodillo</h6>
                                <div class="border p-2 bg-light small" style="white-space: pre-wrap; min-height: 40px;">@item.Rodillo</div>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Información de calidad y estado -->
                        <DxFormLayoutItem ColSpanLg="3">
                            <div class="info-section p-2 small">
                                <div>@item.ObsCalidad</div>
                                <div>@item.TextoEstadoCodApli</div>
                            </div>
                        </DxFormLayoutItem>

                        <!-- Información de programación y barniz -->
                        <DxFormLayoutItem ColSpanLg="3">
                            <div class="info-section p-2">
                                <div class="small">
                                    <div><strong>Posición:</strong> @item.Posicion</div>
                                    <div><strong>Flejar:</strong> @(item.Flejar.HasValue && item.Flejar.Value ? "SI" : "NO")</div>
                                    <div><strong>Barniz:</strong> @($"{item.BarnizNecesario}")</div>
                                    <div><strong>Inicio:</strong> @(item.HoraComienzoEstimada?.ToString("dd/MM - HH:mm"))</div>
                                    <div><strong>Fin:</strong> @(item.HoraFinEstimada?.ToString("dd/MM - HH:mm"))</div>
                                </div>
                            </div>
                        </DxFormLayoutItem>
                    </DxFormLayoutGroup>

                    <!-- Línea separadora -->
                    <DxFormLayoutGroup ColSpanLg="12" Decoration="FormLayoutGroupDecoration.None">
                        <Items>
                            <DxFormLayoutItem ColSpanLg="12" CssClass="border-bottom">
                                <span style="width: 100%; display:block;"></span>
                            </DxFormLayoutItem>
                        </Items>
                    </DxFormLayoutGroup>
                </DxFormLayoutGroup>
            }

            <!-- Sumario del grupo - Replicando GroupFooter del reporte -->
            @if (ListaPedidos.Any())
            {
                <span class="sumario">
                    <span>Pos: @($"{ListaPedidos.Min(item => item.Posicion):F0} - {ListaPedidos.Max(item => item.Posicion):F0}")</span>
                    <span>Hojas: @($"{ListaPedidos.Sum(item => item.HojasPedido):N0}")</span>
                    <span>Superficie: @($"{ListaPedidos.Sum(item => item.Sup):N2}") m2</span>
                    <span>
                        @{
                            var listaAux = ListaPedidos.Where(o => o.HoraFinEstimada != null).ToList();
                            var finTexto = listaAux.Any()
                                ? listaAux.Max(item => item.HoraFinEstimada.Value).ToString("dd/MM/yyyy hh:mm")
                                : "";
                        }
                        Fin: @finTexto
                    </span>
                    <span>Barniz: @($"{ListaPedidos.Where(o => o.BarnizNecesario.HasValue).Sum(item => item.BarnizNecesario):N2}") kg</span>
                </span>
            }
        </Items>
    </DxFormLayoutGroup>
</div>

@code {
    [Parameter] public List<PedidoProgramacionEnPantallaDTO> ListaPedidos { get; set; }
    [Parameter] public string Cabecera { get; set; }

    // Callbacks para comunicación con el componente padre
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, string, Task> OnObservacionUpdated { get; set; }
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, Enums.ProgramacionesPantalla_EstadosPedido, Task> OnEstadoPedidoUpdatedViaHub { get; set; }
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, Task> OnShowPopup { get; set; }

    // Propiedades de roles pasadas desde el padre
    [Parameter] public bool EsJefeTurno { get; set; }
    [Parameter] public bool EsEncargado { get; set; }
    [Parameter] public bool EsRodillos { get; set; }
    [Parameter] public bool EsMaquina { get; set; }

    // Método para cambiar el estado de un pedido
    private async Task CambiarEstado(PedidoProgramacionEnPantallaDTO pedido, Enums.ProgramacionesPantalla_EstadosPedido nuevoEstado)
    {
        await OnEstadoPedidoUpdatedViaHub(pedido, nuevoEstado);
    }

    // Método para obtener las clases CSS del botón según el estado
    private string GetButtonClass(PedidoProgramacionEnPantallaDTO item, Enums.ProgramacionesPantalla_EstadosPedido estado)
    {
        var baseClass = "btn btn-sm";
        var isSelected = item.IdEstado == (int)estado;

        if (isSelected)
        {
            return $"{baseClass} btn-info";
        }
        else
        {
            return $"{baseClass} btn-light";
        }
    }

    // Método para obtener la clase CSS de la fila según el estado
    private string GetRowCssClass(PedidoProgramacionEnPantallaDTO item)
    {
        var estadoNombre = item.NombreEstado?.ToLower().Trim() ?? "sinempezar";
        return $"row-estado-{estadoNombre}";
    }

    private async Task SetDatosPopUp(PedidoProgramacionEnPantallaDTO pedido, string texto)
    {
        await OnShowPopup(pedido, texto);
    }
}
