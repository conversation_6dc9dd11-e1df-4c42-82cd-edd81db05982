@using ProgramadorGeneralBLZ.Shared
@using ProgramadorGeneralBLZ.Shared.DTO

<div class="litografia-v2-container">
    <!-- Cabecera del grupo - Replicando GroupHeader del reporte -->
    <div class="card mb-3">
        <div class="card-header bg-primary text-white p-2">
            <div class="row align-items-center">
                <div class="col-md-10">
                    <h4 class="mb-0 fw-bold">@Cabecera</h4>
                </div>
                <div class="col-md-2 text-end">
                    @if (ListaPedidos.Any())
                    {
                        <small class="fw-bold">Pos: @($"{ListaPedidos.Min(item => item.Posicion):F0}-{ListaPedidos.Max(item => item.Posicion):F0}")</small>
                    }
                </div>
            </div>
        </div>

        <!-- Subcabecera con títulos de columnas - Replicando GroupHeader2 del reporte -->
        <div class="card-header bg-light border-bottom p-2">
            <div class="row text-center fw-bold text-primary">
                <div class="col-2">Cliente</div>
                <div class="col-1">Pedido</div>
                <div class="col-2">Hojalata</div>
                <div class="col-1">Hojas</div>
                <div class="col-1">Diámetro</div>
                <div class="col-4">Tintas</div>
                <div class="col-1">Acciones</div>
            </div>
        </div>

        <!-- Datos usando estructura similar al reporte -->
        <div class="card-body p-0">
            @foreach (var item in ListaPedidos)
            {
                <div class="@GetRowCssClass(item) border-bottom p-2" id="@item.Idpedido.ToString()">
                    <!-- Fila principal de datos -->
                    <div class="row align-items-start">
                        <!-- Cliente -->
                        <div class="col-2">
                            <div class="cliente-info">
                                <strong>@item.IdCliente</strong><br/>
                                <small>@item.NombreCliente</small>
                            </div>
                        </div>

                        <!-- Pedido -->
                        <div class="col-1 text-center">
                            <div class="pedido-info">
                                <strong class="fs-5">@($"{item.Idpedido:00-0-0000}")</strong>
                            </div>
                        </div>

                        <!-- Hojalata -->
                        <div class="col-2">
                            <div class="hojalata-info">
                                <div>@item.CaractHjlta</div>
                                @if (!string.IsNullOrEmpty(item.Plano))
                                {
                                    <small class="text-primary"><strong>Plano:</strong> @item.Plano</small>
                                }
                            </div>
                        </div>

                        <!-- Hojas -->
                        <div class="col-1 text-center">
                            <strong>@($"{item.HojasPedido:N0}")</strong>
                        </div>

                        <!-- Diámetro/Formato -->
                        <div class="col-1 text-center">
                            @if (item.Formato != null)
                            {
                                @($"{item.Formato.Value:F2}")
                            }
                        </div>

                        <!-- Tintas - Replicando la estructura del reporte -->
                        <div class="col-4">
                            <div class="tintas-container">
                                <div class="row g-1">
                                    <div class="col-6">
                                        @foreach (var tinta in GetListaTintas_T01_T06(item))
                                        {
                                            @if (!string.IsNullOrEmpty(tinta.T))
                                            {
                                                <div class="tinta-row d-flex justify-content-between">
                                                    <span class="tinta-nombre">@tinta.T</span>
                                                    <span class="tinta-formula">@tinta.FT</span>
                                                </div>
                                            }
                                        }
                                    </div>
                                    <div class="col-6">
                                        @{
                                            var mostrarT07 = GetSiMostrarTintaExt(item.Idaplicacion, item.T07Ext);
                                            var mostrarT08 = GetSiMostrarTintaExt(item.Idaplicacion, item.T08Ext);
                                            var mostrarT09 = GetSiMostrarTintaExt(item.Idaplicacion, item.T09Ext);
                                            var mostrarT10 = GetSiMostrarTintaExt(item.Idaplicacion, item.T10Ext);
                                            var mostrarT11 = GetSiMostrarTintaExt(item.Idaplicacion, item.T11Ext);
                                        }
                                        @if (mostrarT07)
                                        {
                                            <div class="tinta-row d-flex justify-content-between">
                                                <span class="tinta-nombre">@item.T07Ext</span>
                                                <span class="tinta-formula">@item.FT07Ext</span>
                                            </div>
                                        }
                                        @if (mostrarT08)
                                        {
                                            <div class="tinta-row d-flex justify-content-between">
                                                <span class="tinta-nombre">@item.T08Ext</span>
                                                <span class="tinta-formula">@item.FT08Ext</span>
                                            </div>
                                        }
                                        @if (mostrarT09)
                                        {
                                            <div class="tinta-row d-flex justify-content-between">
                                                <span class="tinta-nombre">@item.T09Ext</span>
                                                <span class="tinta-formula">@item.FT09Ext</span>
                                            </div>
                                        }
                                        @if (mostrarT10)
                                        {
                                            <div class="tinta-row d-flex justify-content-between">
                                                <span class="tinta-nombre">@item.T10Ext</span>
                                                <span class="tinta-formula">@item.FT10Ext</span>
                                            </div>
                                        }
                                        @if (mostrarT11)
                                        {
                                            <div class="tinta-row d-flex justify-content-between">
                                                <span class="tinta-nombre">@item.T11Ext</span>
                                                <span class="tinta-formula">@item.FT11Ext</span>
                                            </div>
                                        }
                                        <div class="mt-1">
                                            <strong>Flejar: @(item.Flejar != null && item.Flejar.Value ? "SI" : "NO")</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Acciones y Estado -->
                        <div class="col-1">
                            <div class="acciones-container">
                                <!-- Botones de estado -->
                                <div class="btn-group-vertical btn-group-sm mb-2 d-grid gap-1" role="group">
                                    <button type="button"
                                            class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado)"
                                            title="Empezar orden"
                                            disabled="@(!EsMaquina)"
                                            @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Empezado))">
                                        <i class="oi oi-media-play"></i>
                                    </button>
                                    <button type="button"
                                            class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido)"
                                            title="Detener orden"
                                            disabled="@(!EsMaquina)"
                                            @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Detenido))">
                                        <i class="oi oi-media-stop"></i>
                                    </button>
                                    <button type="button"
                                            class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado)"
                                            title="Finalizar orden"
                                            disabled="@(!EsMaquina)"
                                            @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Terminado))">
                                        <i class="oi oi-circle-check"></i>
                                    </button>
                                    <button type="button"
                                            class="@GetButtonClass(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado)"
                                            title="Sacar orden"
                                            disabled="@(!EsMaquina)"
                                            @onclick="@(() => CambiarEstado(item, Enums.ProgramacionesPantalla_EstadosPedido.Retirado))">
                                        <i class="oi oi-circle-x"></i>
                                    </button>
                                </div>
                                
                                <!-- Botones de acción -->
                                <div class="btn-group-vertical btn-group-sm d-grid gap-1">
                                    <DxButton Click="@(async () => await SetDatosPopUp(item, "Rodillo"))"
                                              Visible="@EsRodillos" 
                                              RenderStyle="ButtonRenderStyle.Info" 
                                              SizeMode="SizeMode.Small"
                                              Text="Rod"/>
                                    <DxButton Click="@(async () => await SetDatosPopUp(item, "Nota"))"
                                              Visible="@(EsEncargado || EsJefeTurno)" 
                                              RenderStyle="ButtonRenderStyle.Info" 
                                              SizeMode="SizeMode.Small"
                                              Text="Nota"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Información adicional expandible - Segunda fila -->
                    <div class="row mt-2">
                        <!-- Rodillo -->
                        <div class="col-4">
                            <div class="info-section">
                                <h6 class="text-primary mb-1">Rodillo</h6>
                                <div class="border p-2 bg-light small" style="white-space: pre-wrap; min-height: 60px;">@item.Rodillo</div>
                            </div>
                        </div>

                        <!-- Información adicional -->
                        <div class="col-4">
                            <div class="info-section">
                                <h6 class="text-primary mb-1">Información</h6>
                                <div class="small">
                                    <div><span class="text-primary fw-bold">Min. Hojas:</span> @item.MinHojas</div>
                                    <div><span class="text-primary fw-bold">Formato:</span> 
                                        @if (item.Formato != null)
                                        {
                                            @($"{item.Formato.Value:F2}")
                                        }
                                    </div>
                                    <div><span class="text-primary fw-bold">Máculas:</span> @(item.Maculas != null && item.Maculas.Value ? "SI" : "NO")</div>
                                </div>
                            </div>
                        </div>

                        <!-- Fechas y posición -->
                        <div class="col-4">
                            <div class="info-section">
                                <h6 class="text-primary mb-1">Programación</h6>
                                <div class="small">
                                    <div><strong>Posición:</strong> @item.Posicion</div>
                                    <div><strong>Inicio:</strong> @(item.HoraComienzoEstimada?.ToString("dd/MM - HH:mm"))</div>
                                    <div><strong>Fin:</strong> @(item.HoraFinEstimada?.ToString("dd/MM - HH:mm"))</div>
                                    @if (item.RequeridoEnFecha.HasValue)
                                    {
                                        <div><strong>Requerido:</strong> @(item.RequeridoEnFecha.Value.ToString("dd/MM"))</div>
                                    }
                                    @if (item.Urgente.HasValue && item.Urgente.Value)
                                    {
                                        <div class="text-danger fw-bold">URGENTE</div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Motivo -->
                    @if (!string.IsNullOrEmpty(item.Motivo))
                    {
                        <div class="row mt-2">
                            <div class="col-12">
                                <h6 class="text-primary mb-1">Motivo</h6>
                                <div class="fs-6 fw-bold">@item.Motivo</div>
                            </div>
                        </div>
                    }

                    <!-- Observaciones -->
                    <div class="row mt-2">
                        <div class="col-12">
                            <h6 class="text-primary mb-1">Observaciones</h6>
                            <div class="small">@($"{item.Observaciones} {item.Obspaseposterior} {item.ObsCalidad}")</div>
                            @if (!string.IsNullOrEmpty(item.NotaJefeTurno))
                            {
                                <div class="alert alert-danger mt-2 py-1" style="white-space: pre-wrap;">
                                    <strong>NOTA:</strong> @item.NotaJefeTurno
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Aplicación Simultánea -->
                    @if (item.ApliSimul is not null)
                    {
                        <div class="row mt-2">
                            <div class="col-12">
                                <h6 class="text-primary mb-1">Aplicación Simultánea</h6>
                                <div class="small">
                                    <div><strong>Aplicación Simultánea:</strong> @item.ApliProducto</div>
                                    <div><strong>Forma Flejado:</strong> @item.FormaFlejado</div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Información de estado y ubicación -->
                    <div class="row mt-2">
                        <div class="col-6">
                            <div class="small">@item.CogerDe</div>
                        </div>
                        <div class="col-6">
                            <div class="small">@item.TextoEstadoCodApli</div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Sumario del grupo - Replicando GroupFooter del reporte -->
        @if (ListaPedidos.Any())
        {
            <div class="card-footer bg-secondary text-white">
                <div class="row text-center">
                    <div class="col">
                        <strong>Hojas:</strong> @($"{ListaPedidos.Sum(item => item.HojasPedido):N0}")
                    </div>
                    <div class="col">
                        <strong>Superficie:</strong> @($"{ListaPedidos.Sum(item => item.Sup):N2}") m²
                    </div>
                    <div class="col">
                        <strong>Fin:</strong> @(ListaPedidos.Where(o => o.HoraFinEstimada != null)
                                                          .Max(item => item.HoraFinEstimada.Value)
                                                          .ToString("dd/MM/yyyy HH:mm"))
                    </div>
                    <div class="col">
                        <strong>Barniz:</strong> @($"{ListaPedidos.Where(o => o.BarnizNecesario.HasValue).Sum(item => item.BarnizNecesario):N2}") kg
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public List<PedidoProgramacionEnPantallaDTO> ListaPedidos { get; set; }
    [Parameter] public string Cabecera { get; set; }

    // Callbacks para comunicación con el componente padre
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, string, Task> OnObservacionUpdated { get; set; }
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, Enums.ProgramacionesPantalla_EstadosPedido, Task> OnEstadoPedidoUpdatedViaHub { get; set; }
    [Parameter] public Func<PedidoProgramacionEnPantallaDTO, string, Task> OnShowPopup { get; set; }

    // Propiedades de roles pasadas desde el padre
    [Parameter] public bool EsJefeTurno { get; set; }
    [Parameter] public bool EsEncargado { get; set; }
    [Parameter] public bool EsRodillos { get; set; }
    [Parameter] public bool EsMaquina { get; set; }

    // Método para cambiar el estado de un pedido
    private async Task CambiarEstado(PedidoProgramacionEnPantallaDTO pedido, Enums.ProgramacionesPantalla_EstadosPedido nuevoEstado)
    {
        await OnEstadoPedidoUpdatedViaHub(pedido, nuevoEstado);
    }

    // Método para obtener las clases CSS del botón según el estado
    private string GetButtonClass(PedidoProgramacionEnPantallaDTO item, Enums.ProgramacionesPantalla_EstadosPedido estado)
    {
        var baseClass = "btn btn-sm";
        var isSelected = item.IdEstado == (int)estado;

        if (isSelected)
        {
            return $"{baseClass} btn-info";
        }
        else
        {
            return $"{baseClass} btn-light";
        }
    }

    // Método para obtener la clase CSS de la fila según el estado
    private string GetRowCssClass(PedidoProgramacionEnPantallaDTO item)
    {
        var estadoNombre = item.NombreEstado?.ToLower().Trim() ?? "sinempezar";
        return $"row-estado-{estadoNombre}";
    }

    private async Task SetDatosPopUp(PedidoProgramacionEnPantallaDTO pedido, string texto)
    {
        await OnShowPopup(pedido, texto);
    }

    private List<(string T, string FT)> GetListaTintas_T01_T06(PedidoProgramacionEnPantallaDTO pedido)
    {
        List<(string T, string FT)> lista = new();

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T01Ext))
            lista.Add((pedido.T01Ext, pedido.FT01Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T01Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T02Ext))
            lista.Add((pedido.T02Ext, pedido.FT02Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T02Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T03Ext))
            lista.Add((pedido.T03Ext, pedido.FT03Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T03Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T04Ext))
            lista.Add((pedido.T04Ext, pedido.FT04Ext));
        else if (GetSiMostrarTintaInt(pedido.Idaplicacion))
            lista.Add((pedido.T04Int, string.Empty));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T05Ext))
            lista.Add((pedido.T05Ext, pedido.FT05Ext));
        else
            lista.Add(("", ""));

        if (GetSiMostrarTintaExt(pedido.Idaplicacion, pedido.T06Ext))
            lista.Add((pedido.T06Ext, pedido.FT06Ext));
        else
            lista.Add(("", ""));

        return lista;
    }

    private bool GetSiMostrarTintaExt(int? idAplicacion, string T)
    {
        // las tintas exteriores únicamente pueden ir con esta config
        return idAplicacion == 999 || (idAplicacion == 997 && !string.IsNullOrEmpty(T) && T.Contains("1000"));
    }

    private bool GetSiMostrarTintaInt(int? idAplicacion)
    {
        // las tintas interiores únicamente pueden ir con esta config
        return idAplicacion == 999 || idAplicacion == 998;
    }
}
