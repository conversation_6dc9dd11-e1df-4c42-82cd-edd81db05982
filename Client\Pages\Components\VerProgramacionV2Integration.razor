@* Ejemplo de integración de los componentes V2 en VerProgramacion.razor *@
@* Este archivo muestra cómo integrar los nuevos componentes V2 *@

@using ProgramadorGeneralBLZ.Shared.DTO

@* Agregar este toggle en la cabecera de VerProgramacion.razor *@
<DxFormLayoutItem ColSpanLg="2" CaptionCssClass="cabecera-reporte" Caption="Vista:">
    <DxCheckBox @bind-Checked="@UseV2Components" 
                Text="Usar componentes V2" 
                CssClass="cabecera-reporte"/>
</DxFormLayoutItem>

@* Reemplazar el switch actual en VerProgramacion.razor con este código: *@

@code {
    // Agregar esta propiedad en VerProgramacion.razor
    private bool UseV2Components = false;

    // Reemplazar el switch case existente con este método:
    private RenderFragment RenderProgramacionComponent(IGrouping<(string ApliProducto, int Orden), PedidoProgramacionEnPantallaDTO> grupo)
    {
        var tipoPedido = grupo.FirstOrDefault()?.TipoPedido ?? string.Empty;
        
        return tipoPedido switch
        {
            "Barnizado" when UseV2Components => 
                @<VerProgramacionBarnizadoV2 Cabecera="@grupo.Key.ApliProducto"
                                             ListaPedidos="@grupo.ToList()"
                                             OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
                                             OnShowPopup="HandleShowPopupObservaciones"
                                             EsJefeTurno="EsJefeTurno"
                                             EsEncargado="EsEncargado"
                                             EsRodillos="EsRodillos"
                                             EsMaquina="EsMaquina" />,
            
            "Barnizado" => 
                @<VerProgramacionBarnizado Cabecera="@grupo.Key.ApliProducto"
                                           ListaPedidos="@grupo.ToList()"
                                           OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
                                           OnShowPopup="HandleShowPopupObservaciones"
                                           EsJefeTurno="EsJefeTurno"
                                           EsEncargado="EsEncargado"
                                           EsRodillos="EsRodillos"
                                           EsMaquina="EsMaquina" />,
            
            _ when UseV2Components => 
                @<VerProgramacionLitografiaV2Final Cabecera="@grupo.Key.ApliProducto"
                                                   ListaPedidos="@grupo.ToList()"
                                                   OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
                                                   OnShowPopup="HandleShowPopupObservaciones"
                                                   EsJefeTurno="EsJefeTurno"
                                                   EsEncargado="EsEncargado"
                                                   EsRodillos="EsRodillos"
                                                   EsMaquina="EsMaquina" />,
            
            _ => 
                @<VerProgramacionLitografia Cabecera="@grupo.Key.ApliProducto"
                                            ListaPedidos="@grupo.ToList()"
                                            OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
                                            OnShowPopup="HandleShowPopupObservaciones"
                                            EsJefeTurno="EsJefeTurno"
                                            EsEncargado="EsEncargado"
                                            EsRodillos="EsRodillos"
                                            EsMaquina="EsMaquina" />
        };
    }

    // Método similar para elementos individuales:
    private RenderFragment RenderProgramacionComponentSingle(PedidoProgramacionEnPantallaDTO item)
    {
        var tipoPedido = item.TipoPedido ?? string.Empty;
        
        return tipoPedido switch
        {
            "Barnizado" when UseV2Components => 
                @<VerProgramacionBarnizadoV2 Cabecera="@item.ApliProducto"
                                             ListaPedidos="@(new List<PedidoProgramacionEnPantallaDTO> { item })"
                                             OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
                                             OnShowPopup="HandleShowPopupObservaciones"
                                             EsJefeTurno="EsJefeTurno"
                                             EsEncargado="EsEncargado"
                                             EsRodillos="EsRodillos"
                                             EsMaquina="EsMaquina" />,
            
            "Barnizado" => 
                @<VerProgramacionBarnizado Cabecera="@item.ApliProducto"
                                           ListaPedidos="@(new List<PedidoProgramacionEnPantallaDTO> { item })"
                                           OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
                                           OnShowPopup="HandleShowPopupObservaciones"
                                           EsJefeTurno="EsJefeTurno"
                                           EsEncargado="EsEncargado"
                                           EsRodillos="EsRodillos"
                                           EsMaquina="EsMaquina" />,
            
            _ when UseV2Components => 
                @<VerProgramacionLitografiaV2Final Cabecera="@item.ApliProducto"
                                                   ListaPedidos="@(new List<PedidoProgramacionEnPantallaDTO> { item })"
                                                   OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
                                                   OnShowPopup="HandleShowPopupObservaciones"
                                                   EsJefeTurno="EsJefeTurno"
                                                   EsEncargado="EsEncargado"
                                                   EsRodillos="EsRodillos"
                                                   EsMaquina="EsMaquina" />,
            
            _ => 
                @<VerProgramacionLitografia Cabecera="@item.ApliProducto"
                                            ListaPedidos="@(new List<PedidoProgramacionEnPantallaDTO> { item })"
                                            OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
                                            OnShowPopup="HandleShowPopupObservaciones"
                                            EsJefeTurno="EsJefeTurno"
                                            EsEncargado="EsEncargado"
                                            EsRodillos="EsRodillos"
                                            EsMaquina="EsMaquina" />
        };
    }
}

@* 
INSTRUCCIONES DE INTEGRACIÓN:

1. Agregar la referencia al CSS en _Host.cshtml o App.razor:
   <link href="css/VerProgramacionV2.css" rel="stylesheet" />

2. En VerProgramacion.razor, agregar la propiedad:
   private bool UseV2Components = false;

3. Agregar el toggle en la cabecera (línea ~67):
   <DxFormLayoutItem ColSpanLg="2" CaptionCssClass="cabecera-reporte" Caption="Vista:">
       <DxCheckBox @bind-Checked="@UseV2Components" 
                   Text="Usar componentes V2" 
                   CssClass="cabecera-reporte"/>
   </DxFormLayoutItem>

4. Reemplazar los switch cases existentes (líneas ~92-110 y ~127-145) con llamadas a:
   @RenderProgramacionComponent(grupo)
   @RenderProgramacionComponentSingle(item)

5. Agregar los métodos RenderProgramacionComponent y RenderProgramacionComponentSingle al @code

6. Importar los nuevos componentes en _Imports.razor o al inicio del archivo:
   @using ProgramadorGeneralBLZ.Client.Pages.Components

NOTA: Cambiar "VerProgramacionLitografiaV2Final" por "VerProgramacionLitografiaV2" 
      una vez que se renombre el archivo correctamente.
*@
